class MinesweeperGame {
    constructor() {
        this.board = [];
        this.gameState = 'ready'; // ready, playing, won, lost
        this.timer = 0;
        this.timerInterval = null;
        this.mineCount = 0;
        this.flagCount = 0;
        this.revealedCount = 0;
        
        // 难度设置
        this.difficulties = {
            easy: { rows: 9, cols: 9, mines: 10 },
            medium: { rows: 16, cols: 16, mines: 40 },
            hard: { rows: 16, cols: 30, mines: 99 }
        };
        
        this.currentDifficulty = 'easy';
        this.config = this.difficulties[this.currentDifficulty];
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeGame();
    }
    
    initializeElements() {
        this.gameBoard = document.getElementById('game-board');
        this.mineCountDisplay = document.getElementById('mine-count');
        this.timerDisplay = document.getElementById('timer');
        this.resetBtn = document.getElementById('reset-btn');
        this.gameMessage = document.getElementById('game-message');
        this.difficultyBtns = document.querySelectorAll('.difficulty-btn');
    }
    
    setupEventListeners() {
        this.resetBtn.addEventListener('click', () => this.resetGame());
        
        this.difficultyBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.changeDifficulty(e.target.dataset.difficulty);
            });
        });
        
        // 防止右键菜单
        this.gameBoard.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    changeDifficulty(difficulty) {
        this.currentDifficulty = difficulty;
        this.config = this.difficulties[difficulty];
        
        // 更新按钮状态
        this.difficultyBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.difficulty === difficulty);
        });
        
        this.resetGame();
    }
    
    initializeGame() {
        this.gameState = 'ready';
        this.timer = 0;
        this.flagCount = 0;
        this.revealedCount = 0;
        this.mineCount = this.config.mines;
        
        this.updateDisplay();
        this.createBoard();
        this.renderBoard();
        
        this.gameMessage.textContent = '点击任意格子开始游戏';
        this.gameMessage.className = 'game-message';
        this.resetBtn.textContent = '😊';
    }
    
    createBoard() {
        this.board = [];
        for (let row = 0; row < this.config.rows; row++) {
            this.board[row] = [];
            for (let col = 0; col < this.config.cols; col++) {
                this.board[row][col] = {
                    isMine: false,
                    isRevealed: false,
                    isFlagged: false,
                    neighborMines: 0
                };
            }
        }
    }
    
    placeMines(firstClickRow, firstClickCol) {
        let minesPlaced = 0;
        while (minesPlaced < this.config.mines) {
            const row = Math.floor(Math.random() * this.config.rows);
            const col = Math.floor(Math.random() * this.config.cols);
            
            // 确保第一次点击的位置和周围不放雷
            const isFirstClickArea = Math.abs(row - firstClickRow) <= 1 && 
                                   Math.abs(col - firstClickCol) <= 1;
            
            if (!this.board[row][col].isMine && !isFirstClickArea) {
                this.board[row][col].isMine = true;
                minesPlaced++;
            }
        }
        
        this.calculateNeighborMines();
    }
    
    calculateNeighborMines() {
        for (let row = 0; row < this.config.rows; row++) {
            for (let col = 0; col < this.config.cols; col++) {
                if (!this.board[row][col].isMine) {
                    this.board[row][col].neighborMines = this.countNeighborMines(row, col);
                }
            }
        }
    }
    
    countNeighborMines(row, col) {
        let count = 0;
        for (let i = -1; i <= 1; i++) {
            for (let j = -1; j <= 1; j++) {
                const newRow = row + i;
                const newCol = col + j;
                if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol].isMine) {
                    count++;
                }
            }
        }
        return count;
    }
    
    isValidCell(row, col) {
        return row >= 0 && row < this.config.rows && col >= 0 && col < this.config.cols;
    }
    
    renderBoard() {
        this.gameBoard.innerHTML = '';
        this.gameBoard.style.gridTemplateColumns = `repeat(${this.config.cols}, 1fr)`;
        
        for (let row = 0; row < this.config.rows; row++) {
            for (let col = 0; col < this.config.cols; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                cell.addEventListener('click', (e) => this.handleCellClick(e, row, col));
                cell.addEventListener('contextmenu', (e) => this.handleRightClick(e, row, col));
                
                this.gameBoard.appendChild(cell);
            }
        }
    }
    
    handleCellClick(e, row, col) {
        e.preventDefault();
        
        if (this.gameState === 'won' || this.gameState === 'lost') return;
        if (this.board[row][col].isRevealed || this.board[row][col].isFlagged) return;
        
        // 第一次点击时放置雷
        if (this.gameState === 'ready') {
            this.gameState = 'playing';
            this.placeMines(row, col);
            this.startTimer();
            this.gameMessage.textContent = '';
        }
        
        this.revealCell(row, col);
        this.updateCellDisplay(row, col);
        this.checkGameState();
    }
    
    handleRightClick(e, row, col) {
        e.preventDefault();
        
        if (this.gameState === 'won' || this.gameState === 'lost') return;
        if (this.board[row][col].isRevealed) return;
        
        this.toggleFlag(row, col);
        this.updateCellDisplay(row, col);
        this.updateDisplay();
    }
    
    revealCell(row, col) {
        if (!this.isValidCell(row, col) || this.board[row][col].isRevealed || this.board[row][col].isFlagged) {
            return;
        }
        
        this.board[row][col].isRevealed = true;
        this.revealedCount++;
        
        // 如果是雷，游戏结束
        if (this.board[row][col].isMine) {
            this.gameState = 'lost';
            return;
        }
        
        // 如果周围没有雷，自动揭开周围的格子
        if (this.board[row][col].neighborMines === 0) {
            for (let i = -1; i <= 1; i++) {
                for (let j = -1; j <= 1; j++) {
                    this.revealCell(row + i, col + j);
                }
            }
        }
    }
    
    toggleFlag(row, col) {
        const cell = this.board[row][col];
        cell.isFlagged = !cell.isFlagged;
        this.flagCount += cell.isFlagged ? 1 : -1;
    }
    
    updateCellDisplay(row, col) {
        const cellElement = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        const cell = this.board[row][col];
        
        cellElement.className = 'cell';
        cellElement.textContent = '';
        
        if (cell.isFlagged) {
            cellElement.classList.add('flagged');
            cellElement.textContent = '🚩';
        } else if (cell.isRevealed) {
            cellElement.classList.add('revealed');
            
            if (cell.isMine) {
                cellElement.classList.add('mine');
                cellElement.textContent = '💣';
            } else if (cell.neighborMines > 0) {
                cellElement.textContent = cell.neighborMines;
                cellElement.classList.add(`number-${cell.neighborMines}`);
            }
        }
    }
    
    updateDisplay() {
        this.mineCountDisplay.textContent = Math.max(0, this.mineCount - this.flagCount).toString().padStart(3, '0');
        this.timerDisplay.textContent = this.timer.toString().padStart(3, '0');
    }
    
    startTimer() {
        this.timerInterval = setInterval(() => {
            this.timer++;
            this.updateDisplay();
        }, 1000);
    }
    
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    checkGameState() {
        if (this.gameState === 'lost') {
            this.revealAllMines();
            this.stopTimer();
            this.gameMessage.textContent = '游戏失败！点击笑脸重新开始';
            this.gameMessage.className = 'game-message lose';
            this.resetBtn.textContent = '😵';
            return;
        }
        
        // 检查是否获胜
        const totalCells = this.config.rows * this.config.cols;
        if (this.revealedCount === totalCells - this.config.mines) {
            this.gameState = 'won';
            this.stopTimer();
            this.gameMessage.textContent = `恭喜获胜！用时 ${this.timer} 秒`;
            this.gameMessage.className = 'game-message win';
            this.resetBtn.textContent = '😎';
        }
    }
    
    revealAllMines() {
        for (let row = 0; row < this.config.rows; row++) {
            for (let col = 0; col < this.config.cols; col++) {
                if (this.board[row][col].isMine) {
                    this.board[row][col].isRevealed = true;
                    this.updateCellDisplay(row, col);
                }
            }
        }
    }
    
    resetGame() {
        this.stopTimer();
        this.initializeGame();
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    new MinesweeperGame();
});
