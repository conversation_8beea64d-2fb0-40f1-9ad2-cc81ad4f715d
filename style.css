* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.game-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #333;
    margin-bottom: 15px;
    font-size: 2.5em;
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.label {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.value {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
    font-family: 'Courier New', monospace;
}

.reset-btn {
    background: none;
    border: 2px solid #ddd;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 2em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    border-color: #667eea;
    transform: scale(1.1);
}

.difficulty-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-btn {
    padding: 10px 20px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.difficulty-btn:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.difficulty-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.game-board-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.game-board {
    display: grid;
    gap: 2px;
    background: #bbb;
    padding: 10px;
    border-radius: 10px;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
}

.cell {
    width: 30px;
    height: 30px;
    background: #e0e0e0;
    border: 2px outset #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    transition: all 0.1s ease;
}

.cell:hover {
    background: #f0f0f0;
}

.cell.revealed {
    background: #fff;
    border: 1px solid #999;
    cursor: default;
}

.cell.mine {
    background: #ff4444;
    color: white;
}

.cell.flagged {
    background: #ffeb3b;
    color: #d32f2f;
}

.cell.number-1 { color: #1976d2; }
.cell.number-2 { color: #388e3c; }
.cell.number-3 { color: #f57c00; }
.cell.number-4 { color: #7b1fa2; }
.cell.number-5 { color: #d32f2f; }
.cell.number-6 { color: #00796b; }
.cell.number-7 { color: #303f9f; }
.cell.number-8 { color: #424242; }

.game-message {
    text-align: center;
    font-size: 1.2em;
    font-weight: bold;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-message.win {
    color: #4caf50;
}

.game-message.lose {
    color: #f44336;
}

@media (max-width: 600px) {
    .game-container {
        padding: 15px;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .difficulty-selector {
        flex-direction: column;
    }
    
    .cell {
        width: 25px;
        height: 25px;
        font-size: 12px;
    }
}
